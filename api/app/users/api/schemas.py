from typing import Optional

from pydantic import BaseModel, Field

from app.db.models import Agent
# 创建内部类的引用
AgentType = Agent.Type


class LoginRequest(BaseModel):
    """登录请求"""

    username: str = Field(..., example="dongjak")
    password: str = Field(..., example="123394")


class UserInfo(BaseModel):
    """用户信息"""

    id: int
    username: str
    can_create_agent: Optional[bool] = Field(None, description="是否可以创建代理")


class LoginResult(BaseModel):
    """登录结果"""

    token: str
    user_info: UserInfo


class SmsLoginRequest(BaseModel):
    """短信登录请求"""

    phone: str
    code: str


class RegisterRequest(BaseModel):
    """注册请求"""

    username: str = Field(..., min_length=3, max_length=20, example="newuser")
    password: str = Field(..., min_length=6, example="newpassword123")
    phone: str = Field(..., pattern=r"^1[3-9]\d{9}$", example="***********")
    code: str = Field(..., min_length=6, max_length=6, example="123456")


class ResetPasswordRequest(BaseModel):
    """重置密码请求"""

    phone: str = Field(..., pattern=r"^1[3-9]\d{9}$", example="***********")
    code: str = Field(..., min_length=6, max_length=6, example="123456")
    new_password: str = Field(..., min_length=6, example="newpassword123")


class UserAccountInfo(BaseModel):
    """用户账户信息"""

    balance: Optional[float] = Field(0, description="账户余额")
    remaining_experience_count: Optional[int] = Field(0, description="剩余体验次数")
    remaining_tokens: Optional[int] = Field(0, description="剩余token数量")
    currency: Optional[str] = Field("CNY", description="货币类型")
